<template>
  <div class="h-screen overflow-hidden relative bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500">
    <!-- 背景装饰 -->
    <div class="absolute inset-0 bg-gradient-to-br from-indigo-500/20 via-purple-500/20 to-pink-500/20 pointer-events-none"></div>
    <!-- 主要内容区域 -->
    <div class="relative z-10 h-full flex flex-col items-center justify-center p-4">
      <!-- 主要内容卡片 - 放大容器 -->
      <div class="max-w-7xl w-full backdrop-blur-lg bg-white/10 rounded-3xl p-6 border border-white/20 shadow-2xl">
         <!-- 系统标题 -->
      <div class="text-center mb-6">
        <h1 class="text-4xl lg:text-5xl font-bold bg-gradient-to-r from-white to-indigo-100 bg-clip-text text-transparent mb-3 drop-shadow-sm">
          智能康复系统
        </h1>
        <p class="text-lg text-white/80 font-light mb-3">
          Intelligent Rehabilitation System
        </p>
        <div class="flex items-center justify-center space-x-2">
          <div :class="[
            'w-2 h-2 rounded-full animate-pulse',
            systemStatusClass === 'status-success' ? 'bg-green-400 shadow-lg shadow-green-400/50' : '',
            systemStatusClass === 'status-warning' ? 'bg-yellow-400 shadow-lg shadow-yellow-400/50' : '',
            systemStatusClass === 'status-error' ? 'bg-red-400 shadow-lg shadow-red-400/50' : '',
            systemStatusClass === 'status-info' ? 'bg-blue-400 shadow-lg shadow-blue-400/50' : ''
          ]"></div>
          <span class="text-sm text-white/60">{{ systemStatusText }}</span>
        </div>
      </div>

        <!-- 主要内容区域 - 重新设计为居中布局 -->
        <div class="flex-1 flex items-center justify-center">
          <div class="w-full max-w-6xl mx-auto grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">

            <!-- 左侧：视频流区域 - 放大显示 -->
            <div class="order-2 lg:order-1">
              <div class="bg-white/95 backdrop-blur-lg rounded-2xl border border-white/20 shadow-xl overflow-hidden">
                <div class="p-4 border-b border-gray-100">
                  <div class="flex items-center justify-between">
                    <span class="font-semibold text-gray-700">实时画面</span>
                    <el-tag :type="isConnected ? 'success' : 'danger'" size="small" class="rounded-full">
                      {{ isConnected ? '已连接' : '未连接' }}
                    </el-tag>
                  </div>
                </div>

                <div class="p-6">
                  <!-- 视频流组件 - 更大的显示区域 -->
                  <div class="relative aspect-video bg-gray-100 rounded-xl overflow-hidden">
                    <VideoStream
                      :width="'100%'"
                      :height="'100%'"
                      class="w-full h-full"
                    />
                    <!-- 人脸检测框叠加层 -->
                    <div
                      v-if="currentState === 'waiting' && faceBbox"
                      class="absolute inset-0 pointer-events-none"
                    >
                      <div
                        :style="{
                          position: 'absolute',
                          left: faceBbox.x + 'px',
                          top: faceBbox.y + 'px',
                          width: faceBbox.width + 'px',
                          height: faceBbox.height + 'px',
                          border: '2px solid #10b981',
                          borderRadius: '8px',
                          backgroundColor: 'rgba(16, 185, 129, 0.1)'
                        }"
                        class="transition-all duration-200"
                      >
                        <!-- 用户ID标签 -->
                        <div
                          v-if="detectedPatientId"
                          class="absolute -top-8 left-0 bg-green-500 text-white text-xs px-2 py-1 rounded"
                        >
                          {{ detectedPatientId }}
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 视频提示信息 -->
                  <div class="mt-4 text-center">
                    <p class="text-sm text-gray-600">请站在摄像头前，保持正面朝向</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- 右侧：系统状态区域 - 居中显示 -->
            <div class="order-1 lg:order-2 flex flex-col items-center space-y-8">

              <!-- 系统Logo和标题 -->
              <div class="text-center">
                <div class="w-24 h-24 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6">
                  <el-icon class="text-4xl text-white">
                    <Monitor />
                  </el-icon>
                </div>
                <h1 class="text-4xl font-bold text-gray-800 mb-3">智能康复系统</h1>
                <p class="text-lg text-gray-600">请站在摄像头前进行身份识别</p>
              </div>

              <!-- 系统状态卡片 -->
              <div class="w-full max-w-md bg-white/95 backdrop-blur-lg rounded-2xl border border-white/20 shadow-xl overflow-hidden">
                <div class="p-8">
                  <!-- 连接状态 -->
                  <div class="flex items-center justify-center mb-8">
                    <div :class="[
                      'w-4 h-4 rounded-full mr-3',
                      isConnected ? 'bg-green-500 animate-pulse' : 'bg-red-500'
                    ]"></div>
                    <span :class="[
                      'text-lg font-medium',
                      isConnected ? 'text-green-600' : 'text-red-600'
                    ]">
                      {{ isConnected ? '系统已连接' : '系统未连接' }}
                    </span>
                  </div>

                  <!-- 当前状态显示 -->
                  <div class="text-center space-y-6">
                    <!-- waiting状态 -->
                    <div v-if="currentState === 'waiting'">
                      <div class="flex items-center justify-center mb-4">
                        <el-icon class="animate-spin mr-3 text-blue-500 text-3xl">
                          <Loading />
                        </el-icon>
                        <span class="text-xl font-semibold text-blue-600">等待用户识别</span>
                      </div>
                      <p class="text-gray-600 mb-4">请保持站立姿势，面向摄像头</p>
                      <div class="bg-blue-50 rounded-lg p-4">
                        <p class="text-sm text-blue-700">系统正在检测您的身份信息...</p>
                      </div>
                    </div>

                    <!-- start_failed状态 -->
                    <div v-else-if="currentState === 'start_failed'">
                      <div class="flex items-center justify-center mb-4">
                        <el-icon class="mr-3 text-red-500 text-3xl">
                          <WarningFilled />
                        </el-icon>
                        <span class="text-xl font-semibold text-red-600">连接失败</span>
                      </div>
                      <p class="text-gray-600 mb-4">{{ connectionError || '无法连接到服务器' }}</p>
                      <div class="bg-red-50 rounded-lg p-4">
                        <p class="text-sm text-red-700">请检查网络连接或联系技术支持</p>
                      </div>
                    </div>

                    <!-- 其他状态的简单显示 -->
                    <div v-else>
                      <div class="flex items-center justify-center mb-4">
                        <el-icon class="mr-3 text-green-500 text-3xl">
                          <CircleCheckFilled />
                        </el-icon>
                        <span class="text-xl font-semibold text-green-600">{{ systemStatusText }}</span>
                      </div>
                      <p class="text-gray-600 mb-4">{{ message || '系统运行正常' }}</p>
                    </div>
                  </div>

                  <!-- 用户信息显示 -->
                  <div v-if="userDisplayName !== '用户'" class="mt-8 pt-6 border-t border-gray-100">
                    <div class="text-center">
                      <p class="text-sm text-gray-500 mb-2">当前用户</p>
                      <p class="text-lg font-semibold text-green-600">{{ userDisplayName }}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 响应式消息显示区域 -->
        <transition
          enter-active-class="transition-all duration-300 ease-out"
          leave-active-class="transition-all duration-200 ease-in"
          enter-from-class="opacity-0 transform translate-y-2"
          enter-to-class="opacity-100 transform translate-y-0"
          leave-from-class="opacity-100 transform translate-y-0"
          leave-to-class="opacity-0 transform translate-y-2"
        >
          <div v-if="currentNotification" class="mt-6">
            <div :class="[
              'p-4 rounded-xl border backdrop-blur-lg',
              notificationTypeClass
            ]">
              <div class="flex items-center">
                <el-icon :class="notificationIconClass" class="mr-3">
                  <CircleCheckFilled v-if="currentNotification.type === 'success'" />
                  <WarningFilled v-else-if="currentNotification.type === 'warning'" />
                  <Loading v-else-if="currentNotification.type === 'info'" />
                  <WarningFilled v-else />
                </el-icon>
                <div class="flex-1">
                  <p :class="notificationTextClass" class="font-medium">
                    {{ currentNotification.content }}
                  </p>
                  <p class="text-xs opacity-75 mt-1">
                    {{ formatNotificationTime(currentNotification.timestamp) }}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </transition>

        <!-- 底部信息 -->
        <div class="text-center mt-8 text-sm text-white/60">
          <p>系统版本 v1.0.0 | 运行时间: {{ systemUptime }}</p>
        </div>
      </div>
    </div>
    <!-- 登录成功过渡动画 -->
    <transition
      enter-active-class="transition-all duration-500 ease-out"
      leave-active-class="transition-all duration-300 ease-in"
      enter-from-class="opacity-0 scale-75"
      enter-to-class="opacity-100 scale-100"
      leave-from-class="opacity-100 scale-100"
      leave-to-class="opacity-0 scale-110"
      appear
    >
      <div v-if="showLoginSuccess" class="fixed inset-0 bg-gradient-to-br from-green-500/95 to-emerald-600/95 backdrop-blur-lg z-50 flex items-center justify-center">
        <div class="text-center max-w-md p-8">
          <div class="mb-6 animate-bounce">
            <el-icon :size="80" class="text-white drop-shadow-lg">
              <CircleCheckFilled />
            </el-icon>
          </div>
          <h2 class="text-3xl font-bold text-white mb-4 drop-shadow-sm">
            登录成功！
          </h2>
          <p class="text-lg text-white/90 mb-6">
            欢迎 {{ userDisplayName }}，正在进入训练系统...
          </p>
          <div class="w-full">
            <el-progress
              :percentage="loginProgress"
              :show-text="false"
              stroke-width="4"
              color="#ffffff"
              class="mb-2"
            />
            <p class="text-sm text-white/80">{{ loginProgress }}%</p>
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useConnectionStore } from '@/stores/connection'
import { usePatientStore } from '@/stores/patient'
import { useNotificationStore } from '@/stores/notification'
import { useWorkflowStore } from '@/stores/workflow'
import { useStateTransition } from '@/composables/useStateTransition'
import { CircleCheckFilled, Loading, WarningFilled, Monitor } from '@element-plus/icons-vue'
import VideoStream from '@/components/VideoStream.vue'

// 使用模块化store
const connectionStore = useConnectionStore()
const patientStore = usePatientStore()
const notificationStore = useNotificationStore()
const workflowStore = useWorkflowStore()
const stateTransition = useStateTransition()
// 响应式数据
const showLoginSuccess = ref(false)
const loginProgress = ref(0)
const systemUptime = ref('00:00:00')
const uptimeTimer = ref(null)
const startTime = ref(Date.now())
// 人脸检测相关
const faceBbox = ref(null)
const detectedPatientId = ref(null)

// 计算属性
const isConnected = computed(() => connectionStore.isConnected)
const currentState = computed(() => workflowStore.currentState)
const userInfo = computed(() => patientStore.userInfo)
const message = computed(() => notificationStore.message)
const connectionError = computed(() => connectionStore.connectionError)

// 响应式消息系统
const currentNotification = computed(() => notificationStore.currentNotification)

// 用户显示名称
const userDisplayName = computed(() => {
  if (userInfo.value?.name) {
    return userInfo.value.name
  } else if (userInfo.value?.patient_id) {
    return `用户 ${userInfo.value.patient_id}`
  }
  return '用户'
})

// 系统状态文本和样式
const systemStatusText = computed(() => {
  switch (currentState.value) {
    case 'waiting':
      return '等待用户识别'
    case 'introduction':
      return '欢迎回来，正在介绍任务'
    case 'start_failed':
      return '连接失败'
    default:
      return message.value || '系统就绪'
  }
})

const systemStatusClass = computed(() => {
  if (!isConnected.value || currentState.value === 'start_failed') {
    return 'status-error'
  }
  switch (currentState.value) {
    case 'waiting':
      return 'status-info'
    case 'introduction':
      return 'status-success'
    default:
      return 'status-success'
  }
})

// 消息通知样式计算属性
const notificationTypeClass = computed(() => {
  if (!currentNotification.value) return ''

  const baseClass = 'bg-white/90 border-white/30'
  switch (currentNotification.value.type) {
    case 'success':
      return `${baseClass} border-green-200 bg-green-50/90`
    case 'warning':
      return `${baseClass} border-yellow-200 bg-yellow-50/90`
    case 'error':
      return `${baseClass} border-red-200 bg-red-50/90`
    case 'info':
    default:
      return `${baseClass} border-blue-200 bg-blue-50/90`
  }
})

const notificationIconClass = computed(() => {
  if (!currentNotification.value) return ''

  switch (currentNotification.value.type) {
    case 'success':
      return 'text-green-500'
    case 'warning':
      return 'text-yellow-500'
    case 'error':
      return 'text-red-500'
    case 'info':
    default:
      return 'text-blue-500'
  }
})

const notificationTextClass = computed(() => {
  if (!currentNotification.value) return ''
  switch (currentNotification.value.type) {
    case 'success':
      return 'text-green-800'
    case 'warning':
      return 'text-yellow-800'
    case 'error':
      return 'text-red-800'
    case 'info':
    default:
      return 'text-blue-800'
  }
})

// 格式化通知时间
const formatNotificationTime = (timestamp) => {
  const now = new Date()
  const time = new Date(timestamp)
  const diff = now - time

  if (diff < 60000) { // 小于1分钟
    return '刚刚'
  } else if (diff < 3600000) { // 小于1小时
    return `${Math.floor(diff / 60000)}分钟前`
  } else {
    return time.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    })
  }
}


// 启动运行时间计时器
const startUptimeTimer = () => {
  uptimeTimer.value = setInterval(() => {
    const elapsed = Date.now() - startTime.value
    const hours = Math.floor(elapsed / 3600000)
    const minutes = Math.floor((elapsed % 3600000) / 60000)
    const seconds = Math.floor((elapsed % 60000) / 1000)
    systemUptime.value = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
  }, 1000)
}


// 显示登录成功动画
const showLoginSuccessAnimation = () => {
  console.log('显示登录成功动画')
  showLoginSuccess.value = true
  loginProgress.value = 0

  // 3秒进度条动画
  const totalDuration = 3000 // 3秒
  const updateInterval = 1000 // 每50ms更新一次
  const progressStep = (100 / totalDuration) * updateInterval
  const progressInterval = setInterval(() => {
    loginProgress.value += progressStep
    if (loginProgress.value >= 100) {
      clearInterval(progressInterval)
      loginProgress.value = 100
      // 动画完成后跳转到任务介绍页面
      setTimeout(() => {
        showLoginSuccess.value = false
        loginProgress.value = 0

        console.log('欢迎动画完成，触发状态转换到任务介绍页面')
        // 手动触发状态转换
        stateTransition.handlePatientValidationSuccess()
      }, 500) // 额外等待500ms让用户看到100%
    }
  }, updateInterval)
}

// 监听用户信息变化，触发登录成功动画
watch(
  () => patientStore.userInfo,
  (newUserInfo) => {
    if (newUserInfo && newUserInfo.patient_id) {
      console.log('检测到用户信息更新，触发登录成功动画');
      showLoginSuccessAnimation();

    }
  },
  { immediate: false }
);

// 在组件挂载时启动校验
onMounted(() => {
  startUptimeTimer()
  console.log('LoginView 组件已挂载');
  // 如果当前状态是waiting且已连接，启动patientId校验
  if (workflowStore.currentState === 'waiting' && connectionStore.isConnected) {
    console.log('开始启动patientId校验');
    patientStore.startPatientValidation(() => connectionStore.patientId);
  }
});

// 在组件卸载时停止校验
onUnmounted(() => {
  console.log('LoginView 组件即将卸载，停止patientId校验');
  patientStore.stopPatientValidation();
});
</script>


