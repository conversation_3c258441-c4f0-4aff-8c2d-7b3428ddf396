<template>
  <!-- 根容器，应用了基础样式和深色背景 -->
  <div class="h-screen overflow-hidden relative bg-gray-900 flex items-center justify-center p-4 login-page-bg">
    <!-- 动态背景光晕，样式完全由 Tailwind 控制 -->
    <div class="circle-bg-anim absolute w-96 h-96 bg-indigo-600/50 rounded-full filter blur-3xl top-[-10%] left-[-5%]"></div>
    <div class="circle-bg-anim absolute w-80 h-80 bg-purple-600/50 rounded-full filter blur-3xl bottom-[-10%] right-[-5%]" style="animation-delay: 5s"></div>
    <div class="circle-bg-anim absolute w-72 h-72 bg-pink-600/50 rounded-full filter blur-3xl bottom-[20%] left-[15%]" style="animation-delay: 10s"></div>

    <!-- 页面内容切换容器 -->
    <transition
      mode="out-in"
      enter-active-class="transition-all duration-500 ease-out"
      leave-active-class="transition-all duration-500 ease-in"
      enter-from-class="opacity-0 scale-95"
      leave-to-class="opacity-0 scale-95"
    >
      <!-- 登录/识别状态视图 -->
      <div v-if="!showSuccessPortal" key="login" class="w-full max-w-2xl flex flex-col items-center gap-8 text-white z-10">
        <!-- 系统标题 -->
        <header class="text-center">
          <!-- 标题使用自定义动画 -->
          <h1 class="system-title-anim text-4xl lg:text-5xl font-bold tracking-wider">
            智能康复系统
          </h1>
          <p class="text-lg text-white/60 font-light tracking-widest mt-2 uppercase">
            Intelligent Rehabilitation System
          </p>
        </header>

        <!-- 核心交互卡片 -->
        <main
          class="w-full max-w-lg aspect-square flex flex-col rounded-3xl p-6 border bg-white/5 border-white/10 backdrop-blur-xl shadow-2xl"
        >
          <!-- 视频流容器 -->
          <div
            class="flex-1 relative rounded-2xl overflow-hidden border-2 transition-all duration-500"
            :class="currentState === 'waiting'
              ? 'border-indigo-500 shadow-lg shadow-indigo-500/40'
              : 'border-transparent'"
          >
            <!-- 扫描线，仅在等待时显示和播放动画 -->
            <div
              v-if="currentState === 'waiting'"
              class="scanner-line-anim absolute top-0 left-0 w-full h-1 bg-cyan-400/80 rounded-full shadow-lg z-10"
              :style="{'--shadow-color-1': '#22d3ee', '--shadow-color-2': '#22d3ee'}"
            ></div>
            
            <VideoStream :width="'100%'" :height="'100%'" class="w-full h-full object-cover" />
            
            <!-- 人脸检测框 -->
            <div
              v-if="currentState === 'waiting' && faceBbox"
              class="absolute inset-0 pointer-events-none"
            >
              <div
                :style="faceBboxStyle"
                class="absolute border-2 rounded-lg transition-all duration-200 border-emerald-400 bg-emerald-400/10 shadow-[0_0_15px_rgba(52,211,153,0.4)]"
              >
                <div
                  v-if="detectedPatientId"
                  class="absolute -top-8 left-0 bg-emerald-500 text-white text-xs font-bold px-3 py-1 rounded"
                >
                  ID: {{ detectedPatientId }}
                </div>
              </div>
            </div>
          </div>

          <!-- 状态信息区域 -->
          <footer class="h-32 pt-6 text-center">
            <transition
              mode="out-in"
              enter-active-class="transition-all duration-300 ease-out"
              leave-active-class="transition-all duration-200 ease-in"
              enter-from-class="opacity-0 transform translate-y-2"
              leave-to-class="opacity-0 transform translate-y-2"
            >
              <!-- 各状态的具体内容 -->
              <div :key="currentState" class="flex flex-col items-center justify-center gap-2">
                <el-icon :size="32" class="mb-2" :class="statusIcon.class">
                  <component :is="statusIcon.component" />
                </el-icon>
                <h2 class="text-xl font-semibold" :class="statusText.class">{{ statusText.title }}</h2>
                <p class="text-sm text-white/60 max-w-xs">{{ statusText.description }}</p>
              </div>
            </transition>
          </footer>
        </main>

        <!-- 底部信息 -->
        <footer class="text-center text-xs text-white/40 tracking-wider">
          系统版本 v1.0.0 | 连接状态: 
          <span :class="isConnected ? 'text-green-400' : 'text-red-400'">
            {{ isConnected ? '已连接' : '未连接' }}
          </span>
        </footer>
      </div>

      <!-- 登录成功过渡动画视图 -->
      <div v-else key="success" class="success-portal-anim fixed inset-0 z-50 flex items-center justify-center">
        <div class="success-content-anim text-center text-white flex flex-col items-center gap-4">
          <el-icon class="success-icon-anim text-7xl text-white">
            <CircleCheckFilled />
          </el-icon>
          <h2 class="text-4xl font-bold">验证成功</h2>
          <p class="text-2xl text-white/80">欢迎您，{{ userDisplayName }}</p>
          <p class="text-base text-white/60">正在进入训练系统...</p>
        </div>
      </div>
    </transition>
  </div>
</template>

<style scoped>
/* 
  此处的 CSS 专注于无法由 Tailwind utility class 实现的复杂动画。
  所有布局、颜色、间距等样式均在模板中通过 Tailwind 类实现。
*/

/* 1. 背景渐变 */
.login-page-bg {
  background: radial-gradient(ellipse at bottom, #1b2735 0%, #090a0f 100%);
}

/* 2. 背景光晕浮动动画 */
.circle-bg-anim {
  animation: float-anim 20s infinite ease-in-out;
}
@keyframes float-anim {
  0%, 100% { transform: translateY(0) translateX(0) scale(1); }
  50% { transform: translateY(-20px) translateX(20px) scale(1.1); }
}

/* 3. 标题文字光泽扫描动画 */
.system-title-anim {
  background: linear-gradient(120deg, #e0e0e0, #ffffff, #e0e0e0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-size: 200% 100%;
  animation: shine-text-anim 5s linear infinite;
}
@keyframes shine-text-anim {
  to { background-position: -200% 0; }
}

/* 4. 视频扫描线动画 */
.scanner-line-anim {
  box-shadow: 0 0 10px var(--shadow-color-1), 0 0 20px var(--shadow-color-2);
  animation: scan-anim 3s infinite cubic-bezier(0.65, 0, 0.35, 1);
}
@keyframes scan-anim {
  0% { transform: translateY(-100%); opacity: 0; }
  10%, 90% { opacity: 1; }
  100% { transform: translateY(calc(var(--video-height, 400px) + 100%)); opacity: 0; }
}

/* 5. 登录成功“传送门”开启效果 */
.success-portal-anim {
  animation: portal-open-anim 0.8s cubic-bezier(0.76, 0, 0.24, 1) forwards;
}
@keyframes portal-open-anim {
  from { clip-path: circle(0% at 50% 50%); }
  to { clip-path: circle(75% at 50% 50%); }
}

/* 6. 成功内容淡入动画 */
.success-content-anim {
  animation: content-fade-in-anim 1.2s ease forwards;
  opacity: 0;
}
@keyframes content-fade-in-anim {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

/* 7. 成功图标“弹出”动画 */
.success-icon-anim {
  transform: scale(0);
  animation: check-pop-in-anim 0.5s 0.6s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
}
@keyframes check-pop-in-anim {
  to { transform: scale(1); }
}
</style>

<script setup>
import { ref, computed, onMounted, watch ,onUnmounted} from 'vue';
import { useConnectionStore } from '@/stores/connection';
import { usePatientStore } from '@/stores/patient';
import { useWorkflowStore } from '@/stores/workflow';
import { useStateTransition } from '@/composables/useStateTransition';
import { CircleCheckFilled, Loading, WarningFilled } from '@element-plus/icons-vue';
import VideoStream from '@/components/VideoStream.vue';

// ... (store 定义)
const connectionStore = useConnectionStore();
const patientStore = usePatientStore();
const workflowStore = useWorkflowStore();
const stateTransition = useStateTransition();
// 动画控制
const showSuccessPortal = ref(false);
const faceBbox = ref(null); // 假设从store或事件获取
const detectedPatientId = ref(null); // 假设从store或事件获取

// 计算属性
const isConnected = computed(() => connectionStore.isConnected);
const currentState = computed(() => workflowStore.currentState);
const connectionError = computed(() => connectionStore.connectionError);
const userDisplayName = computed(() => patientStore.userInfo?.name || `用户 ${patientStore.userInfo?.patient_id}` || '您');

// 动态人脸框样式
const faceBboxStyle = computed(() => {
  if (!faceBbox.value) return {};
  return {
    left: `${faceBbox.value.x}px`,
    top: `${faceBbox.value.y}px`,
    width: `${faceBbox.value.width}px`,
    height: `${faceBbox.value.height}px`,
  };
});

// **优化点：将状态逻辑整合到计算属性中，使模板更干净**
const statusIcon = computed(() => {
  switch (currentState.value) {
    case 'waiting': return { component: Loading, class: 'text-indigo-400 animate-spin' };
    case 'start_failed': return { component: WarningFilled, class: 'text-red-400' };
    default: return { component: CircleCheckFilled, class: 'text-emerald-400' };
  }
});

const statusText = computed(() => {
  switch (currentState.value) {
    case 'waiting': return {
      title: '等待身份识别',
      description: '请正对摄像头，系统将自动验证您的身份',
      class: 'text-white/90'
    };
    case 'start_failed': return {
      title: '连接失败',
      description: connectionError.value || '无法连接到核心服务',
      class: 'text-red-400'
    };
    default: return {
      title: '系统就绪',
      description: '一切准备就绪，祝您康复顺利！',
      class: 'text-emerald-400'
    };
  }
});

// 重构后的登录成功动画触发函数
const showLoginSuccessAnimation = () => {
  if (showSuccessPortal.value) return; // 防止重复触发
  showSuccessPortal.value = true;
  // 移除手动调用 handlePatientValidationSuccess，让 useStateTransition 的监听器自动处理
  // 这样避免了重复执行状态转换逻辑
  setTimeout(() => {
    showSuccessPortal.value = false;
    stateTransition.handlePatientValidationSuccess();
  }, 1500); // 匹配CSS动画时长
  console.log('[LoginView] 校验成功，等待自动状态转换...');
};

// 监听器保持不变
watch(() => patientStore.userInfo, (newUserInfo) => {
  if (newUserInfo && newUserInfo.patient_id) {
    showLoginSuccessAnimation();
  }
}, { deep: true });

// onMounted 逻辑保持不变，并添加patientId校验逻辑
onMounted(() => {
  // 为扫描线动画设置容器高度
  const videoWrapper = document.querySelector('.video-stream-wrapper');
  if (videoWrapper) {
    const height = videoWrapper.offsetHeight;
    document.documentElement.style.setProperty('--video-height', `${height}px`);
  }
    // 如果当前状态是waiting且已连接，启动patientId校验
  if (workflowStore.currentState === 'waiting' && connectionStore.isConnected) {
    console.log('开始启动patientId校验');
    patientStore.startPatientValidation(() => connectionStore.patientId);
  }
  // ... 其他 onMounted 逻辑
});
onUnmounted(() => {
  console.log('LoginView 组件即将卸载，停止patientId校验');
  patientStore.stopPatientValidation();
});
</script>