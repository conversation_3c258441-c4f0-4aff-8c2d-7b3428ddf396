/**
 * 调试训练会话问题的脚本
 * 用于检查训练会话启动时的状态
 */

// 在浏览器控制台中运行此脚本来调试训练问题
function debugTrainingSession() {
  console.log('=== 训练会话调试信息 ===')
  
  // 检查 stores 状态
  const workflowStore = window.workflowStore || (window.Vue && window.Vue.useWorkflowStore && window.Vue.useWorkflowStore())
  const trainingStore = window.trainingStore || (window.Vue && window.Vue.useTrainingStore && window.Vue.useTrainingStore())
  const connectionStore = window.connectionStore || (window.Vue && window.Vue.useConnectionStore && window.Vue.useConnectionStore())
  
  console.log('1. Workflow Store 状态:')
  console.log('   - 当前状态:', workflowStore?.currentState)
  
  console.log('2. Training Store 状态:')
  console.log('   - 当前动作:', trainingStore?.currentAction)
  console.log('   - 动作列表长度:', trainingStore?.actionList?.length)
  console.log('   - 动作列表:', trainingStore?.actionList)
  
  console.log('3. Connection Store 状态:')
  console.log('   - 连接状态:', connectionStore?.isConnected)
  console.log('   - 姿态关键点数量:', connectionStore?.poseKeypoints?.length)
  console.log('   - 姿态关键点示例:', connectionStore?.poseKeypoints?.slice(0, 3))
  
  // 检查训练会话状态
  console.log('4. 训练会话状态检查:')
  if (!trainingStore?.currentAction) {
    console.warn('   ❌ 没有当前动作 - 这是问题的根源!')
    console.log('   💡 建议：调用 trainingStore.initializeActions()')
  } else {
    console.log('   ✅ 当前动作存在:', trainingStore.currentAction.action_type)
  }
  
  if (!connectionStore?.isConnected) {
    console.warn('   ❌ 连接未建立')
  } else {
    console.log('   ✅ 连接已建立')
  }
  
  if (!connectionStore?.poseKeypoints || connectionStore.poseKeypoints.length === 0) {
    console.warn('   ❌ 没有姿态关键点数据')
  } else {
    console.log('   ✅ 姿态关键点数据存在，数量:', connectionStore.poseKeypoints.length)
  }
  
  console.log('=== 调试信息结束 ===')
}

// 自动运行调试
if (typeof window !== 'undefined') {
  // 延迟执行，确保 stores 已初始化
  setTimeout(debugTrainingSession, 2000)
}

// 导出函数供手动调用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { debugTrainingSession }
} else if (typeof window !== 'undefined') {
  window.debugTrainingSession = debugTrainingSession
}
