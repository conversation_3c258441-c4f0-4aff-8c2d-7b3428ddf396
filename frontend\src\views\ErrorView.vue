<template>
  <div class="error-page">
    <!-- ... 模板部分保持不变 ... -->
    <div class="scanline"></div>
    <div class="content">
      <h1 class="shine-text">
        系统启动出错
      </h1>
      
      <p class="sub-heading">
        很抱歉，我们无法连接到核心服务。
        <br>
        请检查您的网络连接或稍后重试。
      </p>
      
      <div class="action-buttons">
        <el-button 
          type="primary" 
          size="large" 
          :icon="RefreshRight"
          @click="manualRetry"
          :loading="isRetrying"
          round
        >
          手动重试
        </el-button>
        <el-button 
          size="large"
          :icon="House"
          @click="goHome"
          round
        >
          返回首页
        </el-button>
      </div>

      <div v-if="!isRetrying" class="countdown-text">
        系统将在 <span class="countdown-time">{{ countdown }}</span> 秒后自动重试...
      </div>
      <div v-else class="countdown-text">
        正在尝试重新连接，请稍候...
      </div>
    </div>
  </div>
</template>

<script setup>
// 1. 导入 watch API
import { ref, onMounted, onUnmounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import { useConnectionStore } from '@/stores/connection';
import { useWorkflowStore } from '@/stores/workflow';
import { RefreshRight, House } from '@element-plus/icons-vue';
import  webSocketService  from '@/services/websocket';

const router = useRouter();
const connectionStore = useConnectionStore();
const workflowStore = useWorkflowStore();

const initialCountdown = 10;
const countdown = ref(initialCountdown);
const isRetrying = ref(false);
let timer = null;

// --- 倒计时与重试逻辑 (保持不变) ---
const startCountdown = () => {
  if (timer) clearInterval(timer);
  countdown.value = initialCountdown;
  isRetrying.value = false;

  timer = setInterval(async () => {
    countdown.value--;
    if (countdown.value <= 0) {
      clearInterval(timer);
      await triggerRetry();
      // 如果重试失败, 并且状态仍然是 'start_failed', 再次开始倒计时
      if (workflowStore.currentState === 'start_failed') {
        startCountdown();
      }
    }
  }, 1000);
};

const triggerRetry = async () => {
  isRetrying.value = true;
  console.log("正在触发重试机制...");
  try {
    await webSocketService.connect();
    // 连接成功后，我们不再做任何事，而是等待 `watch` 生效
  } catch (error) {
    console.error("重试连接失败:", error);
  } finally {
    isRetrying.value = false;
  }
};

const manualRetry = async () => {
  if (isRetrying.value) return;
  clearInterval(timer);
  await triggerRetry();
  if (workflowStore.currentState === 'start_failed') {
    startCountdown();
  }
};

// 2. ✨ 新增：使用 watch 监听 store 状态变化 ✨
watch(
  () => workflowStore.currentState, // 我们要监听的目标是 store 中的 currentState
  (newState, oldState) => {
    console.log(`[Watch] Store state changed from '${oldState}' to '${newState}'`);
    // 成功的标志是状态从 'start_failed' 变为 'waiting'
    // 'waiting' 是系统初始化成功后的第一个状态
    if (newState === 'waiting' && oldState === 'start_failed') {
      console.log("✅ 连接成功且系统已初始化！准备跳转到登录页...");
      // 在跳转前，清理掉本页面的定时器，这是个好习惯
      if (timer) {
        clearInterval(timer);
      }
      // 执行路由跳转
      router.push('/login');
    }
  }
);

onMounted(() => {
  // 确保初始状态是失败，然后才开始倒计时
  workflowStore.forceSetState('start_failed');
  startCountdown();
});

onUnmounted(() => {
  if (timer) {
    clearInterval(timer);
  }
});
</script>

<style scoped>
.error-page {
  @apply bg-gray-900 text-gray-200 min-h-screen flex items-center justify-center relative overflow-hidden font-sans;
  font-family: 'Consolas', 'Monaco', 'monospace';
}

.content {
  @apply text-center z-10 p-8 flex flex-col items-center;
}

/* 1. 全新的 "Shine" 文本动画 */
.shine-text {
  @apply text-5xl md:text-7xl font-extrabold uppercase tracking-widest bg-clip-text text-transparent;
  background-image: linear-gradient(
    to right,
    #4f4f4f 0%,
    #ffffff 50%,
    #4f4f4f 100%
  );
  background-size: 200% auto;
  animation: shine 5s linear infinite;
}

@keyframes shine {
  to {
    background-position: -200% center;
  }
}

.sub-heading {
  @apply text-lg md:text-xl text-gray-400 my-8 max-w-2xl mx-auto leading-relaxed;
}

.action-buttons {
  @apply flex justify-center gap-4 mb-8;
}

/* 2. 倒计时文本样式 */
.countdown-text {
  @apply text-gray-500 text-base mt-4 transition-opacity duration-300;
}

.countdown-time {
  @apply font-bold text-cyan-400 mx-1;
}

/* 扫描线动画 (保持不变) */
.scanline {
  @apply absolute top-0 left-0 w-full h-full pointer-events-none;
  z-index: 5;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.02) 50%, rgba(255, 255, 255, 0));
  background-size: 100% 4px;
  animation: scanline-anim 8s linear infinite;
}

@keyframes scanline-anim {
  from { background-position: 0 0; }
  to { background-position: 0 100%; }
}
</style>