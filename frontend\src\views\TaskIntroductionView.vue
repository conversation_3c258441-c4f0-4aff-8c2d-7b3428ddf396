
<template>
  <div class="min-h-screen bg-gray-900 text-white flex flex-col items-center justify-center p-4 sm:p-6 overflow-hidden">
    <!-- 背景辉光效果 (保持不变) -->
    <div class="absolute top-0 left-0 w-1/2 h-full bg-gradient-to-br from-blue-500/20 via-transparent to-transparent blur-3xl animate-pulse-slow"></div>
    <div class="absolute bottom-0 right-0 w-1/2 h-full bg-gradient-to-tl from-purple-600/20 via-transparent to-transparent blur-3xl animate-pulse-slow-delay"></div>

    <!-- 主容器 (保持不变) -->
    <div class="w-full max-w-6xl mx-auto z-10">
      <!-- 欢迎标题区域 (保持不变) -->
      <div class="text-center mb-6 animate-fade-in-down">
        <h1 class="text-4xl sm:text-5xl font-bold mb-3">
          欢迎回来，<span class="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-500">{{ userDisplayName }}</span>！
        </h1>
        <p class="text-lg text-gray-400">
          今日为您准备了 {{ actionList.length }} 个训练任务，请做好准备。
        </p>
      </div>

      <!-- 任务展示区 【核心改动：增加高度】 -->
      <div class="relative h-[28rem] sm:h-[32rem] flex items-center justify-center mb-6">
        <transition-group
          name="task-card"
          tag="div"
          class="w-full h-full"
        >
          <div
            v-for="(action, index) in actionList"
            :key="action.action_id || index"
            class="absolute w-full h-full flex items-center justify-center"
            :style="getCardStyle(index)"
          >
            <!-- 任务卡片容器 【核心改动：限制最大宽度】 -->
            <div class="w-full max-w-3xl h-full bg-white/5 backdrop-blur-xl rounded-2xl p-6 sm:p-8 border border-white/10 shadow-2xl flex flex-col">
              <!-- 卡片头部 【核心改动：增大元素和字体】 -->
              <div class="flex items-start justify-between mb-6">
                <div class="flex items-center">
                  <div class="w-14 h-14 sm:w-16 sm:h-16 rounded-full flex items-center justify-center text-white font-bold text-2xl bg-gradient-to-br from-blue-500 to-purple-600 shadow-lg">
                    {{ index + 1 }}
                  </div>
                  <div class="ml-5">
                    <h3 class="text-3xl sm:text-4xl font-semibold text-white">
                      {{ getActionDisplayName(action) }}
                    </h3>
                    <p class="text-base text-gray-400 mt-1">
                      {{ getSideDisplayName(action.side) }}
                    </p>
                  </div>
                </div>
                <!-- 难度级别和状态标签 (保持不变) -->
                <div class="flex flex-col items-end space-y-2 flex-shrink-0">
                  <div :class="[
                    'text-sm font-bold px-3 py-1.5 rounded-full flex items-center',
                    getDifficultyInfo(action.difficulty_level).class
                  ]">
                    <span>难度：{{ getDifficultyInfo(action.difficulty_level).text }}</span>
                  </div>
                  <div v-if="index === currentTaskIndex" class="flex items-center text-sm font-medium bg-blue-500/20 text-blue-300 px-3 py-1 rounded-full">
                    <div class="w-2 h-2 bg-blue-400 rounded-full animate-pulse mr-2"></div>
                    正在介绍
                  </div>
                </div>
              </div>
              
              <!-- 卡片主体内容 【核心改动：调整布局和字体】 -->
              <div class="flex-grow grid grid-cols-1 gap-6 mt-2">
                <div class="bg-black/20 rounded-lg p-5">
                  <h4 class="text-lg font-medium text-gray-300 mb-3 border-b border-white/10 pb-3">动作要点</h4>
                  <p class="text-base text-gray-400 leading-relaxed">
                    {{ getActionDescription(action) }}
                  </p>
                </div>
                <div class="bg-black/20 rounded-lg p-5">
                  <h4 class="text-lg font-medium text-gray-300 mb-3 border-b border-white/10 pb-3">训练目标</h4>
                  <p class="text-base text-gray-400 leading-relaxed">
                    {{ getActionGoal(action) }}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </transition-group>
      </div>

      <!-- 倒计时区域 【核心改动：增大尺寸】 -->
      <div class="flex flex-col items-center animate-fade-in-up">
        <div class="relative w-48 h-48 sm:w-52 sm:h-52">
          <svg class="w-full h-full" viewBox="0 0 100 100">
            <circle class="text-gray-700" stroke-width="7" stroke="currentColor" fill="transparent" r="45" cx="50" cy="50" />
            <circle
              class="text-blue-500"
              stroke-width="7"
              :stroke-dasharray="circumference"
              :stroke-dashoffset="progressOffset"
              stroke-linecap="round"
              stroke="currentColor"
              fill="transparent"
              r="45"
              cx="50"
              cy="50"
              transform="rotate(-90 50 50)"
              style="transition: stroke-dashoffset 1s linear;"
            />
          </svg>
          <div class="absolute inset-0 flex flex-col items-center justify-center">
            <span class="text-5xl sm:text-6xl font-bold text-white">{{ remainingTime }}</span>
            <span class="text-base text-gray-400 mt-1">秒后开始</span>
          </div>
        </div>
        <p class="mt-4 text-gray-400">
          系统将在倒计时结束后自动进入训练模式
        </p>
      </div>
    </div>
  </div>
</template>

为了匹配JS中更平滑的 `cubic-bezier` 过渡，我们同样更新CSS中的过渡定义。



<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useTrainingStore } from '@/stores/training'
import { useWorkflowStore } from '@/stores/workflow'
import { usePatientStore } from '@/stores/patient'
import { useStateTransition } from '@/composables/useStateTransition'

const trainingStore = useTrainingStore()
const workflowStore = useWorkflowStore()
const patientStore = usePatientStore()
const stateTransition = useStateTransition()

// --- 响应式数据 ---
const DURATION_PER_TASK = 1 // 每个任务介绍5秒
const actionList = computed(() => trainingStore.actionList || [])
const TOTAL_DURATION = computed(() => {
  return actionList.value.length > 0 ? DURATION_PER_TASK * actionList.value.length : 0;
})

const remainingTime = ref(0)
const currentTaskIndex = ref(0)
let taskIntroTimer = null
let countdownTimer = null

// --- 计算属性 ---
const userDisplayName = computed(() => {
  const userInfo = patientStore.userInfo || {}
  return userInfo.patient_name || `用户 ${userInfo.patient_id}` || '用户'
})
const circumference = 2 * Math.PI * 45;
const progressOffset = computed(() => {
  if (TOTAL_DURATION.value === 0) return circumference;
  const progress = (TOTAL_DURATION.value - remainingTime.value) / TOTAL_DURATION.value;
  return circumference * (1 - progress);
});

// --- 方法 ---
const ACTION_DETAILS = {
  'shoulder_touch': {
    name: '对侧触肩',
    description: '身体站直，用一只手触摸对侧的肩膀，然后交替进行，保持动作的稳定与流畅。',
    goal: '增强肩部关节的灵活性和身体的协调能力。'
  },
  'arm_raise': {
    name: '手臂上举',
    description: '保持躯干稳定，缓慢将手臂从体侧向上举起至最高点，感受肩部的完全伸展。',
    goal: '提升肩关节的活动范围，强化上肢核心肌群。'
  },
  'leg_lift': {
    name: '交替抬腿',
    description: '站立位，保持上半身挺直，交替将膝盖向上抬高至髋部水平，锻炼核心平衡。',
    goal: '强化下肢肌肉力量与身体的动态平衡能力。'
  },
  'squat': {
    name: '标准深蹲',
    description: '双脚与肩同宽，背部保持挺直，臀部向后下方坐，如同坐在一张看不见的椅子上。',
    goal: '构建下肢与核心肌群力量，改善身体姿态与稳定性。'
  },
  'finger_touch': { // 新增动作
    name: '指尖对触',
    description: '将拇指指尖依次与其他四指的指尖进行轻轻触碰，动作要清晰、有节奏。',
    goal: '提高手指的精细运动能力、灵活性和协调性。'
  },
  'palm_flip': { // 新增动作
    name: '手掌翻转',
    description: '将前臂放置于平面上，快速、有节奏地进行手心向上和手心向下的翻转动作。',
    goal: '锻炼前臂旋转肌群，提升手腕的快速协调能力。'
  },
  'default': {
    name: '未知动作',
    description: '请遵循系统屏幕上的实时指导完成相应动作。',
    goal: '全面促进身体机能的恢复与提升。'
  }
};

const getActionDetails = (action) => {
  if (!action || !action.action_type) return ACTION_DETAILS.default;
  return ACTION_DETAILS[action.action_type] || ACTION_DETAILS.default;
}
const getActionDisplayName = (action) => getActionDetails(action).name
const getActionDescription = (action) => getActionDetails(action).description
const getActionGoal = (action) => getActionDetails(action).goal
const getSideDisplayName = (side) => {
  if (!side) return '双侧';
  switch (side.toLowerCase()) {
    case 'left': return '左侧';
    case 'right': return '右侧';
    default: return '双侧';
  }
}
const getDifficultyInfo = (level) => {
  if (!level) {
    return { text: '常规', class: 'bg-gray-500/20 text-gray-300' };
  }
  switch (level.toLowerCase()) {
    case 'easy':
      return { text: '简单', class: 'bg-green-500/20 text-green-300' };
    case 'medium':
      return { text: '中等', class: 'bg-yellow-500/20 text-yellow-300' };
    case 'hard':
      return { text: '困难', class: 'bg-red-500/20 text-red-300' };
    default:
      return { text: '常规', class: 'bg-gray-500/20 text-gray-300' };
  }
};
const getCardStyle = (index) => {
  const offset = index - currentTaskIndex.value;
  const total = actionList.value.length;

  // 复杂的 offset 计算是为了处理循环播放时的最短路径
  let effectiveOffset = offset;
  if (total > 2 && Math.abs(offset) > total / 2) {
    effectiveOffset = offset > 0 ? offset - total : offset + total;
  }

  // 根据偏移量计算样式
  if (effectiveOffset === 0) {
    // 当前卡片
    return {
      transform: 'translateX(0) scale(1)',
      opacity: '1',
      zIndex: 10,
      transition: 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)'
    };
  } else if (effectiveOffset === 1) {
    // 下一张卡片，在右侧待命，【核心改动：偏移更远】
    return {
      transform: 'translateX(80%) scale(0.7)',
      opacity: '0.2',
      zIndex: 5,
      transition: 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)'
    };
  } else {
    return {
      transform: 'translateX(-80%) scale(0.7)',
      opacity: '0',
      zIndex: 1,
      transition: 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)'
    };
  }
};
const startIntroductionCycle = () => {
  if (!actionList.value.length) return;
  remainingTime.value = TOTAL_DURATION.value;
  taskIntroTimer = setInterval(() => {
    currentTaskIndex.value = (currentTaskIndex.value + 1) % actionList.value.length;
  }, DURATION_PER_TASK * 1000);
  countdownTimer = setInterval(() => {
    if (remainingTime.value > 0) remainingTime.value--;
    if (remainingTime.value <= 0) handleCountdownFinish();
  }, 1000);
}
const handleCountdownFinish = () => {
  cleanupTimers();
  stateTransition.handleIntroductionComplete();
}
const cleanupTimers = () => {
  if (taskIntroTimer) clearInterval(taskIntroTimer);
  if (countdownTimer) clearInterval(countdownTimer);
}
onMounted(() => { startIntroductionCycle(); })
onUnmounted(() => { cleanupTimers(); })
</script>

<style scoped>
/* 自定义动画 */
@keyframes fade-in-down {
  from { opacity: 0; transform: translateY(-20px); }
  to { opacity: 1; transform: translateY(0); }
}
@keyframes fade-in-up {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}
@keyframes pulse-slow {
  0%, 100% { opacity: 0.1; }
  50% { opacity: 0.2; }
}
@keyframes pulse-slow-delay {
  0%, 100% { opacity: 0.1; }
  50% { opacity: 0.2; }
}

.animate-fade-in-down { animation: fade-in-down 0.8s ease-out forwards; }
.animate-fade-in-up { animation: fade-in-up 0.8s ease-out 0.2s forwards; }
.animate-pulse-slow { animation: pulse-slow 8s cubic-bezier(0.4, 0, 0.6, 1) infinite; }
.animate-pulse-slow-delay { animation: pulse-slow 8s cubic-bezier(0.4, 0, 0.6, 1) infinite 4s; }

/* 任务卡片切换动画 */
.task-card-move,
.task-card-enter-active,
.task-card-leave-active {
  /* 这个类现在可以被 JS 中的内联 transition 覆盖，但保留作为备用 */
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}
.task-card-enter-from {
  opacity: 0;
  transform: translateX(80%) scale(0.7); /* 匹配 JS 的起始位置 */
}
.task-card-leave-to {
  opacity: 0;
  transform: translateX(-80%) scale(0.7); /* 匹配 JS 的结束位置 */
}
.task-card-leave-active {
  position: absolute;
}
</style>